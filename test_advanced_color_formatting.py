#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار متقدم لتنسيق الألوان مع مقاومة إعدادات الستايل
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from for_all import *
from ستايل import *

# استيراد المفوض المخصص من ملف مراحل المشروع
from مراحل_المشروع import AmountStatusDelegate

class AdvancedColorTest(QMainWindow):
    """نافذة اختبار متقدم للألوان"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار متقدم - تنسيق الألوان مع مقاومة الستايل")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الستايل العام
        apply_stylesheet(self)
        
        # إعداد الواجهة
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        
        # عنوان رئيسي
        title_label = QLabel("اختبار متقدم لتنسيق الألوان مع مقاومة إعدادات الستايل")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # معلومات الاختبار
        info_widget = self.create_info_widget()
        layout.addWidget(info_widget)
        
        # إنشاء التابات للاختبارات المختلفة
        tab_widget = QTabWidget()
        
        # تاب الاختبار الأساسي
        basic_test_tab = self.create_basic_test_tab()
        tab_widget.addTab(basic_test_tab, "الاختبار الأساسي")
        
        # تاب اختبار مع الستايل المطبق
        styled_test_tab = self.create_styled_test_tab()
        tab_widget.addTab(styled_test_tab, "اختبار مع الستايل")
        
        # تاب اختبار المفوض المخصص
        delegate_test_tab = self.create_delegate_test_tab()
        tab_widget.addTab(delegate_test_tab, "اختبار المفوض المخصص")
        
        layout.addWidget(tab_widget)
        
    def create_info_widget(self):
        """إنشاء ويدجت المعلومات"""
        info_widget = QWidget()
        info_layout = QHBoxLayout(info_widget)
        
        # معلومات الألوان
        colors_info = QLabel("""
        <b>الألوان المستخدمة:</b><br>
        🔴 أحمر RGB(231, 76, 60) - غير مدرج<br>
        🟢 أخضر RGB(46, 125, 50) - تم الإدراج
        """)
        colors_info.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        
        # معلومات الحلول
        solutions_info = QLabel("""
        <b>الحلول المطبقة:</b><br>
        • استخدام setData مع Qt.ForegroundRole<br>
        • مفوض مخصص AmountStatusDelegate<br>
        • رسم مخصص للنصوص الملونة
        """)
        solutions_info.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 1px solid #c3e6c3;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        
        info_layout.addWidget(colors_info)
        info_layout.addWidget(solutions_info)
        
        return info_widget
        
    def create_basic_test_tab(self):
        """إنشاء تاب الاختبار الأساسي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        layout.addWidget(QLabel("اختبار أساسي بدون ستايل مخصص:"))
        
        # جدول بسيط
        table = QTableWidget(4, 3)
        table.setHorizontalHeaderLabels(["الرقم", "الوصف", "حالة المبلغ"])
        
        # بيانات اختبار
        test_data = [
            ("1", "مرحلة التصميم", "غير مدرج"),
            ("2", "مرحلة التنفيذ", "تم الإدراج"),
            ("3", "مرحلة المراجعة", "غير مدرج"),
            ("4", "مرحلة التسليم", "تم الإدراج")
        ]
        
        for row, (num, desc, status) in enumerate(test_data):
            table.setItem(row, 0, QTableWidgetItem(num))
            table.setItem(row, 1, QTableWidgetItem(desc))
            
            status_item = QTableWidgetItem(status)
            # استخدام setData مع Qt.ForegroundRole
            if status == "غير مدرج":
                status_item.setData(Qt.ForegroundRole, QColor(231, 76, 60))
            elif status == "تم الإدراج":
                status_item.setData(Qt.ForegroundRole, QColor(46, 125, 50))
            
            table.setItem(row, 2, status_item)
        
        layout.addWidget(table)
        return tab
        
    def create_styled_test_tab(self):
        """إنشاء تاب اختبار مع الستايل المطبق"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        layout.addWidget(QLabel("اختبار مع تطبيق الستايل العام للتطبيق:"))
        
        # جدول مع الستايل المطبق
        table = QTableWidget(4, 3)
        table.setHorizontalHeaderLabels(["الرقم", "الوصف", "حالة المبلغ"])
        
        # تطبيق إعدادات الجدول العامة
        table_setting(table)
        
        # بيانات اختبار
        test_data = [
            ("1", "مرحلة التصميم", "غير مدرج"),
            ("2", "مرحلة التنفيذ", "تم الإدراج"),
            ("3", "مرحلة المراجعة", "غير مدرج"),
            ("4", "مرحلة التسليم", "تم الإدراج")
        ]
        
        for row, (num, desc, status) in enumerate(test_data):
            table.setItem(row, 0, QTableWidgetItem(num))
            table.setItem(row, 1, QTableWidgetItem(desc))
            
            status_item = QTableWidgetItem(status)
            # استخدام setData مع Qt.ForegroundRole
            if status == "غير مدرج":
                status_item.setData(Qt.ForegroundRole, QColor(231, 76, 60))
            elif status == "تم الإدراج":
                status_item.setData(Qt.ForegroundRole, QColor(46, 125, 50))
            
            table.setItem(row, 2, status_item)
        
        layout.addWidget(table)
        return tab
        
    def create_delegate_test_tab(self):
        """إنشاء تاب اختبار المفوض المخصص"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        layout.addWidget(QLabel("اختبار مع المفوض المخصص (الحل الأقوى):"))
        
        # جدول مع المفوض المخصص
        table = QTableWidget(6, 3)
        table.setHorizontalHeaderLabels(["الرقم", "الوصف", "حالة المبلغ"])
        
        # تطبيق إعدادات الجدول العامة
        table_setting(table)
        
        # تطبيق المفوض المخصص على عمود حالة المبلغ
        delegate = AmountStatusDelegate(self)
        table.setItemDelegateForColumn(2, delegate)
        
        # بيانات اختبار موسعة
        test_data = [
            ("1", "مرحلة التصميم الأولي", "غير مدرج"),
            ("2", "مرحلة التصميم التفصيلي", "تم الإدراج"),
            ("3", "مرحلة المراجعة الفنية", "غير مدرج"),
            ("4", "مرحلة التنفيذ", "تم الإدراج"),
            ("5", "مرحلة الاختبار", "غير مدرج"),
            ("6", "مرحلة التسليم النهائي", "تم الإدراج")
        ]
        
        for row, (num, desc, status) in enumerate(test_data):
            table.setItem(row, 0, QTableWidgetItem(num))
            table.setItem(row, 1, QTableWidgetItem(desc))
            table.setItem(row, 2, QTableWidgetItem(status))
        
        layout.addWidget(table)
        
        # إضافة ملاحظة
        note_label = QLabel("ملاحظة: هذا الجدول يستخدم المفوض المخصص الذي يضمن ظهور الألوان بغض النظر عن إعدادات الستايل")
        note_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #856404;
            }
        """)
        layout.addWidget(note_label)
        
        return tab

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق الستايل العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء نافذة الاختبار
    test_window = AdvancedColorTest()
    test_window.show()
    
    print("🎨 تم فتح نافذة الاختبار المتقدم للألوان")
    print("📋 تحقق من الألوان في التابات المختلفة:")
    print("   • الاختبار الأساسي: بدون ستايل")
    print("   • اختبار مع الستايل: مع تطبيق الستايل العام")
    print("   • اختبار المفوض المخصص: الحل الأقوى")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
