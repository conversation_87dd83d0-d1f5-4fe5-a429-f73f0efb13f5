#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من تطبيق تنسيق الألوان في الكود
"""

import re

def verify_color_implementation():
    """التحقق من تطبيق تنسيق الألوان في ملف مراحل المشروع"""
    
    print("🔍 بدء التحقق من تطبيق تنسيق الألوان...")
    print("=" * 60)
    
    try:
        # قراءة ملف مراحل المشروع
        with open('مراحل_المشروع.py', 'r', encoding='utf-8') as file:
            content = file.read()
        
        # التحقق من وجود تلوين في دالة load_phases_data
        phases_color_pattern = r'if col_idx == 8:.*?setForeground\(QColor\(231, 76, 60\)\).*?setForeground\(QColor\(46, 125, 50\)\)'
        phases_match = re.search(phases_color_pattern, content, re.DOTALL)
        
        if phases_match:
            print("✅ تم العثور على تلوين حالة المبلغ في جدول المراحل")
            print("   - اللون الأحمر (231, 76, 60) للحالة 'غير مدرج'")
            print("   - اللون الأخضر (46, 125, 50) للحالة 'تم الإدراج'")
        else:
            print("❌ لم يتم العثور على تلوين حالة المبلغ في جدول المراحل")
        
        # التحقق من وجود تلوين في دالة load_engineers_tasks_data
        engineers_color_pattern = r'if col_idx == 5:.*?setForeground\(QColor\(231, 76, 60\)\).*?setForeground\(QColor\(46, 125, 50\)\)'
        engineers_match = re.search(engineers_color_pattern, content, re.DOTALL)
        
        if engineers_match:
            print("✅ تم العثور على تلوين حالة المبلغ في جدول مهام المهندسين")
            print("   - اللون الأحمر (231, 76, 60) للحالة 'غير مدرج'")
            print("   - اللون الأخضر (46, 125, 50) للحالة 'تم الإدراج'")
        else:
            print("❌ لم يتم العثور على تلوين حالة المبلغ في جدول مهام المهندسين")
        
        # التحقق من استخدام QColor
        qcolor_usage = content.count('QColor(')
        print(f"📊 عدد استخدامات QColor في الملف: {qcolor_usage}")
        
        # التحقق من الدوال المحدثة
        load_phases_data_exists = 'def load_phases_data(self):' in content
        load_engineers_tasks_data_exists = 'def load_engineers_tasks_data(self):' in content
        
        print(f"📋 دالة load_phases_data موجودة: {'✅' if load_phases_data_exists else '❌'}")
        print(f"📋 دالة load_engineers_tasks_data موجودة: {'✅' if load_engineers_tasks_data_exists else '❌'}")
        
        # التحقق من أعمدة حالة المبلغ
        phases_status_column = 'حالة المبلغ' in content
        engineers_status_column = 'حالة_مبلغ_المهندس' in content
        
        print(f"📊 عمود حالة المبلغ في المراحل: {'✅' if phases_status_column else '❌'}")
        print(f"📊 عمود حالة مبلغ المهندس: {'✅' if engineers_status_column else '❌'}")
        
        print("=" * 60)
        
        # ملخص النتائج
        if phases_match and engineers_match:
            print("🎉 تم تطبيق تنسيق الألوان بنجاح في كلا الجدولين!")
            print("📝 التفاصيل:")
            print("   • جدول المراحل: عمود 8 (حالة المبلغ)")
            print("   • جدول مهام المهندسين: عمود 5 (حالة مبلغ المهندس)")
            print("   • الألوان المستخدمة:")
            print("     - أحمر (231, 76, 60): غير مدرج")
            print("     - أخضر (46, 125, 50): تم الإدراج")
            return True
        else:
            print("⚠️ لم يتم تطبيق تنسيق الألوان بشكل كامل")
            return False
            
    except FileNotFoundError:
        print("❌ لم يتم العثور على ملف مراحل_المشروع.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def show_color_examples():
    """عرض أمثلة على الألوان المستخدمة"""
    print("\n🎨 أمثلة على الألوان المستخدمة:")
    print("=" * 40)
    
    colors = {
        "أحمر (غير مدرج)": "RGB(231, 76, 60)",
        "أخضر (تم الإدراج)": "RGB(46, 125, 50)"
    }
    
    for color_name, rgb_value in colors.items():
        print(f"• {color_name}: {rgb_value}")
    
    print("\n📋 مواقع التطبيق:")
    print("• جدول مراحل المشروع - عمود 'حالة المبلغ'")
    print("• جدول مهام المهندسين - عمود 'حالة المبلغ'")

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة التحقق من تطبيق تنسيق الألوان")
    print("📅 تاريخ التحديث: 2025-06-19")
    print()
    
    success = verify_color_implementation()
    show_color_examples()
    
    if success:
        print("\n✅ التحقق مكتمل بنجاح - تم تطبيق جميع التحسينات!")
    else:
        print("\n❌ يرجى مراجعة الكود وإصلاح المشاكل المذكورة أعلاه")

if __name__ == "__main__":
    main()
