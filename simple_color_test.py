#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لتنسيق الألوان
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from for_all import *

def test_color_formatting():
    """اختبار تنسيق الألوان في جدول بسيط"""
    app = QApplication(sys.argv)
    
    # إنشاء نافذة اختبار
    window = QMainWindow()
    window.setWindowTitle("اختبار تنسيق الألوان - حالة المبلغ")
    window.setGeometry(100, 100, 800, 600)
    window.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء الجدول
    table = QTableWidget(6, 4)
    table.setHorizontalHeaderLabels(["الرقم", "الوصف", "حالة المبلغ (مراحل)", "حالة المبلغ (مهندسين)"])
    
    # بيانات اختبار
    test_data = [
        ("1", "مرحلة التصميم الأولي", "غير مدرج", "غير مدرج"),
        ("2", "مرحلة التصميم التفصيلي", "تم الإدراج", "تم الإدراج"),
        ("3", "مرحلة المراجعة", "غير مدرج", "تم الإدراج"),
        ("4", "مرحلة التسليم", "تم الإدراج", "غير مدرج"),
        ("5", "مرحلة المتابعة", "غير مدرج", "غير مدرج"),
        ("6", "مرحلة الإغلاق", "تم الإدراج", "تم الإدراج")
    ]
    
    # إضافة البيانات مع التلوين
    for row, (num, desc, phases_status, engineers_status) in enumerate(test_data):
        # الرقم
        table.setItem(row, 0, QTableWidgetItem(num))
        
        # الوصف
        table.setItem(row, 1, QTableWidgetItem(desc))
        
        # حالة المبلغ للمراحل
        phases_item = QTableWidgetItem(phases_status)
        if phases_status == "غير مدرج":
            phases_item.setForeground(QColor(231, 76, 60))  # أحمر
        elif phases_status == "تم الإدراج":
            phases_item.setForeground(QColor(46, 125, 50))  # أخضر
        table.setItem(row, 2, phases_item)
        
        # حالة المبلغ للمهندسين
        engineers_item = QTableWidgetItem(engineers_status)
        if engineers_status == "غير مدرج":
            engineers_item.setForeground(QColor(231, 76, 60))  # أحمر
        elif engineers_status == "تم الإدراج":
            engineers_item.setForeground(QColor(46, 125, 50))  # أخضر
        table.setItem(row, 3, engineers_item)
    
    # تطبيق إعدادات الجدول
    table.resizeColumnsToContents()
    table.setAlternatingRowColors(True)
    table.setSelectionBehavior(QAbstractItemView.SelectRows)
    
    # إعداد النافذة
    central_widget = QWidget()
    layout = QVBoxLayout(central_widget)
    
    # عنوان
    title = QLabel("اختبار تنسيق الألوان لحالة المبلغ")
    title.setStyleSheet("""
        QLabel {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            text-align: center;
        }
    """)
    title.setAlignment(Qt.AlignCenter)
    layout.addWidget(title)
    
    # معلومات
    info = QLabel("الألوان المطبقة:\n🔴 أحمر: غير مدرج\n🟢 أخضر: تم الإدراج")
    info.setStyleSheet("""
        QLabel {
            font-size: 14px;
            color: #34495e;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
    """)
    layout.addWidget(info)
    
    # الجدول
    layout.addWidget(table)
    
    window.setCentralWidget(central_widget)
    window.show()
    
    print("✅ تم إنشاء جدول اختبار الألوان بنجاح")
    print("🔴 النص الأحمر يمثل: غير مدرج")
    print("🟢 النص الأخضر يمثل: تم الإدراج")
    print("📋 تحقق من الألوان في الجدول المعروض")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_color_formatting()
