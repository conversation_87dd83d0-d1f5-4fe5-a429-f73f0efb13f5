#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تنسيق الألوان لحالة المبلغ في جداول مراحل المشروع
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from for_all import *
from مراحل_المشروع import ProjectPhasesWindow

class ColorFormattingTest(QMainWindow):
    """نافذة اختبار تنسيق الألوان"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار تنسيق الألوان - حالة المبلغ")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الواجهة
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        
        # عنوان
        title_label = QLabel("اختبار تنسيق الألوان لحالة المبلغ")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # معلومات الاختبار
        info_label = QLabel("""
        هذا الاختبار يتحقق من:
        • تطبيق اللون الأحمر على النص "غير مدرج"
        • تطبيق اللون الأخضر على النص "تم الإدراج"
        • في كل من جدول مراحل المشروع وجدول مهام المهندسين
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        layout.addWidget(info_label)
        
        # أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار مع بيانات حقيقية
        test_real_btn = QPushButton("اختبار مع بيانات حقيقية")
        test_real_btn.setIcon(qta.icon('fa5s.database', color='white'))
        test_real_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        test_real_btn.clicked.connect(self.test_with_real_data)
        buttons_layout.addWidget(test_real_btn)
        
        # زر اختبار مع بيانات وهمية
        test_dummy_btn = QPushButton("اختبار مع بيانات وهمية")
        test_dummy_btn.setIcon(qta.icon('fa5s.flask', color='white'))
        test_dummy_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        test_dummy_btn.clicked.connect(self.test_with_dummy_data)
        buttons_layout.addWidget(test_dummy_btn)
        
        # زر اختبار الألوان مباشرة
        test_colors_btn = QPushButton("اختبار الألوان مباشرة")
        test_colors_btn.setIcon(qta.icon('fa5s.palette', color='white'))
        test_colors_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        test_colors_btn.clicked.connect(self.test_colors_directly)
        buttons_layout.addWidget(test_colors_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # منطقة النتائج
        self.results_text = QTextEdit()
        self.results_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                background-color: #2c3e50;
                color: #ecf0f1;
            }
        """)
        self.results_text.setPlainText("جاهز للاختبار...\n")
        layout.addWidget(self.results_text)
        
    def log_result(self, message):
        """إضافة رسالة إلى نتائج الاختبار"""
        self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        
    def test_with_real_data(self):
        """اختبار مع بيانات حقيقية من قاعدة البيانات"""
        self.log_result("🔍 بدء اختبار مع بيانات حقيقية...")
        
        try:
            # بيانات مشروع حقيقي (يجب تعديل المعرف حسب البيانات المتاحة)
            project_data = {
                'id': 1,  # معرف مشروع موجود
                'معرف_العميل': 1,
                'اسم_المشروع': 'مشروع اختبار الألوان',
                'المبلغ': 50000,
                'المدفوع': 20000,
                'الحالة': 'قيد الإنجاز'
            }
            
            self.phases_window = ProjectPhasesWindow(
                parent=self,
                project_data=project_data,
                project_type="تصميم"
            )
            
            self.phases_window.show()
            self.log_result("✅ تم فتح نافذة مراحل المشروع بنجاح")
            self.log_result("📋 تحقق من الألوان في جدولي المراحل ومهام المهندسين")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في الاختبار: {e}")
            
    def test_with_dummy_data(self):
        """اختبار مع بيانات وهمية"""
        self.log_result("🧪 بدء اختبار مع بيانات وهمية...")
        
        try:
            project_data = {
                'id': 999,  # معرف وهمي
                'معرف_العميل': 999,
                'اسم_المشروع': 'مشروع اختبار الألوان الوهمي',
                'المبلغ': 75000,
                'المدفوع': 25000,
                'الحالة': 'قيد الإنجاز'
            }
            
            self.phases_window = ProjectPhasesWindow(
                parent=self,
                project_data=project_data,
                project_type="تصميم"
            )
            
            self.phases_window.show()
            self.log_result("✅ تم فتح نافذة الاختبار الوهمي بنجاح")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في الاختبار الوهمي: {e}")
            
    def test_colors_directly(self):
        """اختبار الألوان مباشرة في جدول"""
        self.log_result("🎨 بدء اختبار الألوان مباشرة...")
        
        try:
            # إنشاء نافذة اختبار مباشر
            test_window = QDialog(self)
            test_window.setWindowTitle("اختبار الألوان المباشر")
            test_window.setGeometry(200, 200, 600, 400)
            test_window.setLayoutDirection(Qt.RightToLeft)
            
            layout = QVBoxLayout(test_window)
            
            # إنشاء جدول اختبار
            table = QTableWidget(4, 3)
            table.setHorizontalHeaderLabels(["الرقم", "الوصف", "حالة المبلغ"])
            
            # إضافة بيانات اختبار
            test_data = [
                ("1", "مرحلة اختبار 1", "غير مدرج"),
                ("2", "مرحلة اختبار 2", "تم الإدراج"),
                ("3", "مرحلة اختبار 3", "غير مدرج"),
                ("4", "مرحلة اختبار 4", "تم الإدراج")
            ]
            
            for row, (num, desc, status) in enumerate(test_data):
                table.setItem(row, 0, QTableWidgetItem(num))
                table.setItem(row, 1, QTableWidgetItem(desc))
                
                # إنشاء عنصر حالة المبلغ مع التلوين
                status_item = QTableWidgetItem(status)
                if status == "غير مدرج":
                    status_item.setForeground(QColor(231, 76, 60))  # أحمر
                elif status == "تم الإدراج":
                    status_item.setForeground(QColor(46, 125, 50))  # أخضر
                    
                table.setItem(row, 2, status_item)
            
            layout.addWidget(table)
            
            # زر إغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(test_window.close)
            layout.addWidget(close_btn)
            
            test_window.show()
            self.log_result("✅ تم إنشاء جدول اختبار الألوان المباشر")
            self.log_result("🔴 النص الأحمر: غير مدرج")
            self.log_result("🟢 النص الأخضر: تم الإدراج")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار الألوان المباشر: {e}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق الستايل العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء نافذة الاختبار
    test_window = ColorFormattingTest()
    test_window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
